import 'package:sqflite/sqflite.dart';
import 'package:tijari_tech/core/constants/database_constants.dart';
import '../database.dart';
import '../../../core/utils/app_utils.dart';
import '../../../core/utils/error_handler.dart';
import '../../../core/config/development_config.dart';
import '../../../core/config/dev_dao_wrapper.dart';

abstract class BaseDao<T> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Abstract methods to be implemented by subclasses
  String get tableName;
  T fromMap(Map<String, dynamic> map);
  Map<String, dynamic> toMap(T entity);

  // Get database instance
  Future<Database> get database async => await _databaseHelper.database;

  // Create (Insert)
  Future<String> insert(T entity) async {
    try {
      final db = await database;
      final map = toMap(entity);

      // Use development wrapper to prepare data with safe defaults
      final preparedMap = DevDaoWrapper.prepareInsertData(
        map,
        tableName: tableName,
      );

      // Add timestamps if not already set
      final now = DateTime.now().toIso8601String();
      preparedMap['created_at'] ??= now;
      preparedMap['updated_at'] ??= now;
      preparedMap['is_synced'] ??= 0;

      // Generate ID if not provided (using dev-aware method)
      if (preparedMap['id'] == null || preparedMap['id'].toString().isEmpty) {
        preparedMap['id'] = DevelopmentConfig.isEnabled
            ? DevelopmentConfig.generateId(tableName)
            : AppUtils.generateId();
      }

      // Log development operation
      DevDaoWrapper.logDatabaseOperation('INSERT', tableName, preparedMap);

      await db.insert(tableName, preparedMap);

      // Log sync action
      await _logSyncAction(preparedMap['id'], 'create');

      AppUtils.logInfo(
          'Inserted record in $tableName with ID: ${preparedMap['id']}');
      return preparedMap['id'];
    } catch (e) {
      AppUtils.logError('Error inserting record in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Read (Select) by ID
  Future<T?> findById(String id) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'id = ? AND deleted_at IS NULL',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return fromMap(maps.first);
      }
      return null;
    } catch (e) {
      AppUtils.logError('Error finding record by ID in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Read (Select) all
  Future<List<T>> findAll({
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'deleted_at IS NULL',
        orderBy:
            orderBy != null ? '$orderBy ${ascending ? 'ASC' : 'DESC'}' : null,
        limit: limit,
        offset: offset,
      );

      return maps.map((map) => fromMap(map)).toList();
    } catch (e) {
      AppUtils.logError('Error finding all records in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Read (Select) with custom where clause
  Future<List<T>> findWhere({
    required String where,
    List<dynamic>? whereArgs,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await database;
      final whereClause = 'deleted_at IS NULL AND ($where)';

      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy:
            orderBy != null ? '$orderBy ${ascending ? 'ASC' : 'DESC'}' : null,
        limit: limit,
        offset: offset,
      );

      return maps.map((map) => fromMap(map)).toList();
    } catch (e) {
      AppUtils.logError(
          'Error finding records with where clause in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Update
  Future<bool> update(String id, T entity) async {
    try {
      final db = await database;
      final map = toMap(entity);

      // Update timestamp
      map['updated_at'] = DateTime.now().toIso8601String();
      map['is_synced'] = 0;

      // Remove ID from map to avoid updating it
      map.remove('id');

      final count = await db.update(
        tableName,
        map,
        where: 'id = ? AND deleted_at IS NULL',
        whereArgs: [id],
      );

      if (count > 0) {
        // Log sync action
        await _logSyncAction(id, 'update');
        AppUtils.logInfo('Updated record in $tableName with ID: $id');
        return true;
      }

      return false;
    } catch (e) {
      AppUtils.logError('Error updating record in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Soft Delete
  Future<bool> delete(String id) async {
    try {
      final db = await database;
      final now = DateTime.now().toIso8601String();

      final count = await db.update(
        tableName,
        {
          'deleted_at': now,
          'updated_at': now,
          'is_synced': 0,
        },
        where: 'id = ? AND deleted_at IS NULL',
        whereArgs: [id],
      );

      if (count > 0) {
        // Log sync action
        await _logSyncAction(id, 'delete');
        AppUtils.logInfo('Soft deleted record in $tableName with ID: $id');
        return true;
      }

      return false;
    } catch (e) {
      AppUtils.logError('Error soft deleting record in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Hard Delete (permanent)
  Future<bool> hardDelete(String id) async {
    try {
      final db = await database;

      final count = await db.delete(
        tableName,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (count > 0) {
        AppUtils.logInfo('Hard deleted record in $tableName with ID: $id');
        return true;
      }

      return false;
    } catch (e) {
      AppUtils.logError('Error hard deleting record in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Count records
  Future<int> count({String? where, List<dynamic>? whereArgs}) async {
    try {
      final db = await database;
      String whereClause = 'deleted_at IS NULL';
      List<dynamic> args = [];

      if (where != null) {
        whereClause += ' AND ($where)';
        if (whereArgs != null) {
          args.addAll(whereArgs);
        }
      }

      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $tableName WHERE $whereClause',
        args.isEmpty ? null : args,
      );

      return result.first['count'] as int;
    } catch (e) {
      AppUtils.logError('Error counting records in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Check if record exists
  Future<bool> exists(String id) async {
    try {
      final record = await findById(id);
      return record != null;
    } catch (e) {
      AppUtils.logError('Error checking if record exists in $tableName', e);
      return false;
    }
  }

  // Get records that need to be synced
  Future<List<T>> getUnsyncedRecords() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        tableName,
        where: 'is_synced = 0',
        orderBy: 'updated_at ASC',
      );

      return maps.map((map) => fromMap(map)).toList();
    } catch (e) {
      AppUtils.logError('Error getting unsynced records in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Mark record as synced
  Future<bool> markAsSynced(String id) async {
    try {
      final db = await database;

      final count = await db.update(
        tableName,
        {'is_synced': 1},
        where: 'id = ?',
        whereArgs: [id],
      );

      return count > 0;
    } catch (e) {
      AppUtils.logError('Error marking record as synced in $tableName', e);
      return false;
    }
  }

  // Batch insert
  Future<List<String>> insertBatch(List<T> entities) async {
    try {
      final db = await database;
      final batch = db.batch();
      final ids = <String>[];
      final now = DateTime.now().toIso8601String();

      for (final entity in entities) {
        final map = toMap(entity);

        // Add timestamps
        map['created_at'] = now;
        map['updated_at'] = now;
        map['is_synced'] = 0;

        // Generate ID if not provided
        if (map['id'] == null || map['id'].toString().isEmpty) {
          map['id'] = AppUtils.generateId();
        }

        batch.insert(tableName, map);
        ids.add(map['id']);
      }

      await batch.commit(noResult: true);

      // Log sync actions
      for (final id in ids) {
        await _logSyncAction(id, 'create');
      }

      AppUtils.logInfo(
          'Batch inserted ${entities.length} records in $tableName');
      return ids;
    } catch (e) {
      AppUtils.logError('Error batch inserting records in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Batch update
  Future<bool> updateBatch(Map<String, T> entities) async {
    try {
      final db = await database;
      final batch = db.batch();
      final now = DateTime.now().toIso8601String();

      entities.forEach((id, entity) {
        final map = toMap(entity);
        map['updated_at'] = now;
        map['is_synced'] = 0;
        map.remove('id'); // Remove ID from map

        batch.update(
          tableName,
          map,
          where: 'id = ? AND deleted_at IS NULL',
          whereArgs: [id],
        );
      });

      await batch.commit(noResult: true);

      // Log sync actions
      for (final id in entities.keys) {
        await _logSyncAction(id, 'update');
      }

      AppUtils.logInfo(
          'Batch updated ${entities.length} records in $tableName');
      return true;
    } catch (e) {
      AppUtils.logError('Error batch updating records in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Clear all records (soft delete)
  Future<bool> clear() async {
    try {
      final db = await database;
      final now = DateTime.now().toIso8601String();

      final count = await db.update(
        tableName,
        {
          'deleted_at': now,
          'updated_at': now,
          'is_synced': 0,
        },
        where: 'deleted_at IS NULL',
      );

      AppUtils.logInfo('Cleared $count records in $tableName');
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error clearing records in $tableName', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Log sync action
  Future<void> _logSyncAction(String recordId, String action) async {
    try {
      final db = await database;
      await db.insert(DatabaseConstants.tableSyncLog, {
        DatabaseConstants.columnSyncLogId: AppUtils.generateId(),
        DatabaseConstants.columnSyncLogTableName: tableName,
        DatabaseConstants.columnSyncLogOperation: action,
        DatabaseConstants.columnSyncLogRecordId: recordId,
        DatabaseConstants.columnSyncLogSyncedAt: 0,
        DatabaseConstants.columnSyncLogCreatedAt:
            DateTime.now().toIso8601String(),
      });
    } catch (e) {
      AppUtils.logError('Error logging sync action', e);
    }
  }

  // Execute raw query
  Future<List<Map<String, dynamic>>> rawQuery(String sql,
      [List<dynamic>? arguments]) async {
    try {
      final db = await database;
      return await db.rawQuery(sql, arguments);
    } catch (e) {
      AppUtils.logError('Error executing raw query', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  // Execute raw insert/update/delete
  Future<int> rawExecute(String sql, [List<dynamic>? arguments]) async {
    try {
      final db = await database;
      return await db.rawUpdate(sql, arguments);
    } catch (e) {
      AppUtils.logError('Error executing raw command', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }
}
