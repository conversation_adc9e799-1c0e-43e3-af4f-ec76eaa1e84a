
import 'development_config.dart';
import '../utils/app_utils.dart';

/// Development-aware DAO wrapper
/// Provides safe defaults and bypasses strict validations for testing
class DevDaoWrapper {
  /// Prepare data for insertion with development-safe defaults
  static Map<String, dynamic> prepareInsertData(
    Map<String, dynamic> originalData, {
    String? tableName,
    String? userId,
    String? branchId,
    String? warehouseId,
    String? unitId,
  }) {
    final data = Map<String, dynamic>.from(originalData);

    if (DevelopmentConfig.useMockData) {
      final safeValues = DevDatabaseHelper.getSafeInsertValues(
        userId: userId,
        branchId: branchId,
        warehouseId: warehouseId,
        unitId: unitId,
      );

      // Add safe defaults for common required fields based on table
      data['created_by'] ??= safeValues['created_by'];
      data['created_at'] ??= safeValues['created_at'];
      data['updated_at'] ??= safeValues['updated_at'];
      data['is_synced'] ??= safeValues['is_synced'];

      // Table-specific fields
      if (tableName != null) {
        _addTableSpecificFields(data, tableName, safeValues);
      }

      // Generate ID if not provided
      if (data['id'] == null || data['id'].toString().isEmpty) {
        data['id'] = DevDatabaseHelper.generateSafeId(tableName);
      }

      DevelopmentConfig.logDevOperation('Insert data prepared', {
        'table': tableName,
        'id': data['id'],
        'created_by': data['created_by'],
      });
    }

    return data;
  }

  /// Add table-specific fields based on table schema
  static void _addTableSpecificFields(
    Map<String, dynamic> data,
    String tableName,
    Map<String, dynamic> safeValues,
  ) {
    switch (tableName) {
      case 'stock_movements':
        // stock_movements table doesn't have approved_by
        data['branch_id'] ??= safeValues['branch_id'];
        data['unit_id'] ??= safeValues['unit_id'];
        break;
        
      case 'stock_adjustments':
        // stock_adjustments table has approved_by
        data['approved_by'] ??= safeValues['approved_by'];
        data['warehouse_id'] ??= safeValues['warehouse_id'];
        break;
    }
  }

  /// Prepare stock movement data with safe defaults
  static Map<String, dynamic> prepareStockMovementData(
    Map<String, dynamic> originalData,
  ) {
    final data = prepareInsertData(originalData, tableName: 'stock_movements');

    if (DevelopmentConfig.useMockData) {
      // Ensure required stock movement fields have safe defaults
      // Note: stock_movements table does NOT have approved_by column
      data['product_id'] ??= 'default-product';
      data['warehouse_id'] ??= DevelopmentConfig.mockWarehouseId;
      data['unit_id'] ??= DevelopmentConfig.mockUnitId;
      data['created_by'] ??= DevelopmentConfig.mockUserId;
      data['movement_type'] ??= 'adjustment';
      data['quantity'] ??= 0.0;
      data['unit_cost'] ??= 0.0;
      data['total_value'] ??=
          (data['quantity'] ?? 0.0) * (data['unit_cost'] ?? 0.0);

      // Remove approved_by if it was accidentally added
      data.remove('approved_by');

      DevelopmentConfig.logDevOperation('Stock movement data prepared', {
        'product_id': data['product_id'],
        'warehouse_id': data['warehouse_id'],
        'unit_id': data['unit_id'],
        'has_approved_by': data.containsKey('approved_by'),
      });
    }

    return data;
  }

  /// Prepare sale data with safe defaults
  static Map<String, dynamic> prepareSaleData(
    Map<String, dynamic> originalData,
  ) {
    final data = prepareInsertData(originalData, tableName: 'sales');

    if (DevelopmentConfig.useMockData) {
      data['customer_id'] ??= 'default-customer';
      data['branch_id'] ??= DevelopmentConfig.mockBranchId;
      data['created_by'] ??= DevelopmentConfig.mockUserId;
      data['sale_date'] ??= DateTime.now().toIso8601String();
      data['invoice_no'] ??= AppUtils.generateInvoiceNumber('SAL');
      data['total_amount'] ??= 0.0;
      data['paid_amount'] ??= 0.0;
      data['due'] ??= 0.0;
      data['status'] ??= 'completed';

      DevelopmentConfig.logDevOperation('Sale data prepared', {
        'invoice_no': data['invoice_no'],
        'customer_id': data['customer_id'],
      });
    }

    return data;
  }

  /// Prepare purchase data with safe defaults
  static Map<String, dynamic> preparePurchaseData(
    Map<String, dynamic> originalData,
  ) {
    final data = prepareInsertData(originalData, tableName: 'purchases');

    if (DevelopmentConfig.useMockData) {
      data['supplier_id'] ??= 'default-supplier';
      data['branch_id'] ??= DevelopmentConfig.mockBranchId;
      data['created_by'] ??= DevelopmentConfig.mockUserId;
      data['purchase_date'] ??= DateTime.now().toIso8601String();
      data['invoice_no'] ??= AppUtils.generateInvoiceNumber('PUR');
      data['total_amount'] ??= 0.0;
      data['paid_amount'] ??= 0.0;
      data['due_amount'] ??= 0.0;
      data['status'] ??= 'completed';

      DevelopmentConfig.logDevOperation('Purchase data prepared', {
        'invoice_no': data['invoice_no'],
        'supplier_id': data['supplier_id'],
      });
    }

    return data;
  }

  /// Prepare stock adjustment data with safe defaults
  static Map<String, dynamic> prepareStockAdjustmentData(
    Map<String, dynamic> originalData,
  ) {
    final data =
        prepareInsertData(originalData, tableName: 'stock_adjustments');

    if (DevelopmentConfig.useMockData) {
      data['warehouse_id'] ??= DevelopmentConfig.mockWarehouseId;
      data['created_by'] ??= DevelopmentConfig.mockUserId;
      data['approved_by'] ??= DevelopmentConfig.mockUserId;
      data['adjustment_date'] ??= DateTime.now().toIso8601String();
      data['reference_no'] ??= AppUtils.generateInvoiceNumber('ADJ');
      data['status'] ??= 'approved';
      data['adjustment_type'] ??= 'manual';

      DevelopmentConfig.logDevOperation('Stock adjustment data prepared', {
        'reference_no': data['reference_no'],
        'warehouse_id': data['warehouse_id'],
      });
    }

    return data;
  }

  /// Handle encryption in development mode
  static String handleEncryption(String value, {bool encrypt = true}) {
    if (DevelopmentConfig.skipEncryption) {
      DevelopmentConfig.logDevOperation('Encryption skipped', {
        'value_length': value.length,
        'encrypt': encrypt,
      });
      return value; // Return plain text in dev mode
    }

    // Perform actual encryption/decryption
    // This would call your actual encryption service
    return value;
  }

  /// Validate foreign key constraints in development mode
  static Future<bool> validateForeignKeys(
    Map<String, dynamic> data,
    String tableName,
  ) async {
    if (DevelopmentConfig.bypassValidation) {
      DevelopmentConfig.logDevOperation('Foreign key validation bypassed', {
        'table': tableName,
        'keys': data.keys.toList(),
      });
      return true;
    }

    // Perform actual foreign key validation
    // This would check if referenced records exist
    return true;
  }

  /// Log database operation for development
  static void logDatabaseOperation(
    String operation,
    String tableName,
    Map<String, dynamic>? data,
  ) {
    if (DevelopmentConfig.logAllOperations) {
      DevelopmentConfig.logDevOperation('Database $operation', {
        'table': tableName,
        'id': data?['id'],
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Create development-safe transaction context
  static Map<String, dynamic> createTransactionContext({
    String? userId,
    String? branchId,
    String? warehouseId,
  }) {
    if (DevelopmentConfig.useMockData) {
      final context = DevUserContext.getCurrentContext();
      return {
        'userId': userId ?? context['userId'],
        'branchId': branchId ?? context['branchId'],
        'warehouseId': warehouseId ?? context['warehouseId'],
        'timestamp': DateTime.now().toIso8601String(),
      };
    }

    return {
      'userId': userId,
      'branchId': branchId,
      'warehouseId': warehouseId,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// Development-aware validation helper for specific business rules
class DevBusinessRuleValidator {
  /// Validate stock levels (bypassed in dev mode)
  static bool validateStockLevel(
    double currentStock,
    double requestedQuantity,
    String operation,
  ) {
    if (DevelopmentConfig.bypassValidation) {
      DevelopmentConfig.logDevOperation('Stock level validation bypassed', {
        'current': currentStock,
        'requested': requestedQuantity,
        'operation': operation,
        'result': true,
      });
      return true;
    }

    // Perform actual stock validation
    if (operation == 'subtract' || operation == 'out') {
      return currentStock >= requestedQuantity;
    }
    return true;
  }

  /// Validate user permissions for operation (bypassed in dev mode)
  static bool validateUserPermission(
    String userId,
    String operation,
    String module,
  ) {
    if (DevelopmentConfig.bypassPermissions) {
      DevelopmentConfig.logDevOperation('User permission validation bypassed', {
        'userId': userId,
        'operation': operation,
        'module': module,
        'result': true,
      });
      return true;
    }

    // Perform actual permission validation
    return true;
  }

  /// Validate branch access (bypassed in dev mode)
  static bool validateBranchAccess(String userId, String branchId) {
    if (DevelopmentConfig.bypassValidation) {
      DevelopmentConfig.logDevOperation('Branch access validation bypassed', {
        'userId': userId,
        'branchId': branchId,
        'result': true,
      });
      return true;
    }

    // Perform actual branch access validation
    return true;
  }

  /// Validate warehouse access (bypassed in dev mode)
  static bool validateWarehouseAccess(String userId, String warehouseId) {
    if (DevelopmentConfig.bypassValidation) {
      DevelopmentConfig.logDevOperation(
          'Warehouse access validation bypassed', {
        'userId': userId,
        'warehouseId': warehouseId,
        'result': true,
      });
      return true;
    }

    // Perform actual warehouse access validation
    return true;
  }
}


