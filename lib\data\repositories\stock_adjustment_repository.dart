import '../local/dao/stock_adjustment_dao.dart';
import '../local/dao/stock_movement_dao.dart';
import '../local/dao/stock_dao.dart';
import '../local/dao/product_dao.dart';
import '../models/stock_adjustment.dart';
import '../models/stock_movement.dart';
import '../../core/utils/app_utils.dart';
import '../../core/exceptions/app_exceptions.dart';

class StockAdjustmentRepository {
  final StockAdjustmentDao _adjustmentDao = StockAdjustmentDao();
  final StockAdjustmentItemDao _itemDao = StockAdjustmentItemDao();
  final StockMovementDao _movementDao = StockMovementDao();
  final StockDao _stockDao = StockDao();
  final ProductDao _productDao = ProductDao();

  // ------------------------------------------------------------------
  // CRUD Operations
  // ------------------------------------------------------------------

  /// إنشاء تسوية مخزون جديدة
  Future<String> createAdjustment(StockAdjustment adjustment) async {
    try {
      await _validateAdjustment(adjustment);
      return await _adjustmentDao.insert(adjustment);
    } catch (e) {
      AppUtils.logError('Error creating stock adjustment', e);
      rethrow;
    }
  }

  /// إنشاء تسوية مخزون مع العناصر
  Future<String> createAdjustmentWithItems({
    required StockAdjustment adjustment,
    required List<StockAdjustmentItem> items,
  }) async {
    try {
      await _validateAdjustment(adjustment);
      await _validateAdjustmentItems(items);

      // Insert adjustment
      final adjustmentId = await _adjustmentDao.insert(adjustment);

      // Insert items
      final itemsWithAdjustmentId = items
          .map((item) => StockAdjustmentItem.create(
                id: item.id,
                adjustmentId: adjustmentId,
                productId: item.productId,
                currentQuantity: item.currentQuantity,
                adjustmentQuantity: item.adjustmentQuantity,
                adjustmentType: item.adjustmentType,
                reason: item.reason,
                notes: item.notes,
              ))
          .toList();

      await _itemDao.insertItems(itemsWithAdjustmentId);

      return adjustmentId;
    } catch (e) {
      AppUtils.logError('Error creating stock adjustment with items', e);
      rethrow;
    }
  }

  /// استرجاع جميع تسويات المخزون
  Future<List<StockAdjustment>> getAllAdjustments({
    String? warehouseId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    String? orderBy,
    bool ascending = false,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _adjustmentDao.getAdjustmentsWithDetails(
        warehouseId: warehouseId,
        status: status,
        fromDate: fromDate,
        toDate: toDate,
        orderBy: orderBy,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      AppUtils.logError('Error getting all adjustments', e);
      throw RepositoryException('Failed to get stock adjustments');
    }
  }

  /// استرجاع تسوية مخزون بالمعرف
  Future<StockAdjustment?> getAdjustmentById(String id) async {
    try {
      return await _adjustmentDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error getting adjustment by id', e);
      throw RepositoryException('Failed to get stock adjustment');
    }
  }

  /// استرجاع عناصر تسوية المخزون
  Future<List<StockAdjustmentItem>> getAdjustmentItems(
      String adjustmentId) async {
    try {
      return await _itemDao.getItemsWithProductDetails(adjustmentId);
    } catch (e) {
      AppUtils.logError('Error getting adjustment items', e);
      throw RepositoryException('Failed to get adjustment items');
    }
  }

  /// تحديث تسوية المخزون
  Future<void> updateAdjustment(String id, StockAdjustment adjustment) async {
    try {
      await _validateAdjustment(adjustment);
      await _adjustmentDao.update(id, adjustment);
    } catch (e) {
      AppUtils.logError('Error updating stock adjustment', e);
      rethrow;
    }
  }

  /// حذف تسوية المخزون
  Future<void> deleteAdjustment(String id) async {
    try {
      final adjustment = await _adjustmentDao.findById(id);
      if (adjustment == null) {
        throw Exception('Stock adjustment not found');
      }

      if (!adjustment.canEdit) {
        throw ValidationException(
            'Cannot delete approved or cancelled adjustment');
      }

      // Delete items first
      await _itemDao.deleteItemsByAdjustmentId(id);

      // Delete adjustment
      final deletedAdjustment = adjustment.markAsDeleted();
      await _adjustmentDao.update(id, deletedAdjustment);
    } catch (e) {
      AppUtils.logError('Error deleting stock adjustment', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Business Operations
  // ------------------------------------------------------------------

  /// اعتماد تسوية المخزون
  Future<void> approveAdjustment(String adjustmentId, String approvedBy) async {
    try {
      final adjustment = await _adjustmentDao.findById(adjustmentId);
      if (adjustment == null) {
        throw Exception('Stock adjustment not found');
      }

      if (!adjustment.canApprove) {
        throw ValidationException('Cannot approve this adjustment');
      }

      // Get adjustment items
      final items = await _itemDao.getItemsWithProductDetails(adjustmentId);
      if (items.isEmpty) {
        throw ValidationException('Cannot approve adjustment without items');
      }

      // Process stock movements and update stock
      for (final item in items) {
        await _processStockAdjustmentItem(
            item, adjustment.warehouseId, approvedBy);
      }

      // Update adjustment status
      final approvedAdjustment = adjustment.approve(approvedBy);
      await _adjustmentDao.update(adjustmentId, approvedAdjustment);

      AppUtils.logInfo('Stock adjustment approved: $adjustmentId');
    } catch (e) {
      AppUtils.logError('Error approving stock adjustment', e);
      rethrow;
    }
  }

  /// إلغاء تسوية المخزون
  Future<void> cancelAdjustment(String adjustmentId) async {
    try {
      final adjustment = await _adjustmentDao.findById(adjustmentId);
      if (adjustment == null) {
        throw Exception('Stock adjustment not found');
      }

      if (!adjustment.canCancel) {
        throw ValidationException('Cannot cancel this adjustment');
      }

      final cancelledAdjustment = adjustment.cancel();
      await _adjustmentDao.update(adjustmentId, cancelledAdjustment);

      AppUtils.logInfo('Stock adjustment cancelled: $adjustmentId');
    } catch (e) {
      AppUtils.logError('Error cancelling stock adjustment', e);
      rethrow;
    }
  }

  /// البحث في تسويات المخزون
  Future<List<StockAdjustment>> searchAdjustments({
    String? searchTerm,
    String? warehouseId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    String? orderBy,
    bool ascending = false,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _adjustmentDao.searchAdjustments(
        searchTerm: searchTerm,
        warehouseId: warehouseId,
        status: status,
        fromDate: fromDate,
        toDate: toDate,
        orderBy: orderBy,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      AppUtils.logError('Error searching adjustments', e);
      throw RepositoryException('Failed to search stock adjustments');
    }
  }

  /// استرجاع إحصائيات تسويات المخزون
  Future<Map<String, dynamic>> getAdjustmentStatistics({
    DateTime? fromDate,
    DateTime? toDate,
    String? warehouseId,
  }) async {
    try {
      return await _adjustmentDao.getAdjustmentStatistics(
        fromDate: fromDate,
        toDate: toDate,
        warehouseId: warehouseId,
      );
    } catch (e) {
      AppUtils.logError('Error getting adjustment statistics', e);
      return {};
    }
  }

  /// إنشاء رقم مرجع تلقائي
  Future<String> generateReferenceNumber() async {
    try {
      return await _adjustmentDao.generateReferenceNumber();
    } catch (e) {
      AppUtils.logError('Error generating reference number', e);
      return 'ADJ-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// استرجاع تسويات المخزون المعلقة
  Future<List<StockAdjustment>> getPendingAdjustments() async {
    try {
      return await _adjustmentDao.getPendingAdjustments();
    } catch (e) {
      AppUtils.logError('Error getting pending adjustments', e);
      return [];
    }
  }

  // ------------------------------------------------------------------
  // Private Methods
  // ------------------------------------------------------------------

  /// التحقق من صحة تسوية المخزون
  Future<void> _validateAdjustment(StockAdjustment adjustment) async {
    if (adjustment.referenceNumber.trim().isEmpty) {
      throw ValidationException('Reference number is required');
    }

    if (adjustment.warehouseId.trim().isEmpty) {
      throw ValidationException('Warehouse is required');
    }

    if (adjustment.reason.trim().isEmpty) {
      throw ValidationException('Reason is required');
    }

    if (adjustment.createdBy.trim().isEmpty) {
      throw ValidationException('Created by is required');
    }

    // Check reference number uniqueness
    final isUnique = await _adjustmentDao.isReferenceNumberUnique(
      adjustment.referenceNumber,
      excludeId: adjustment.id,
    );

    if (!isUnique) {
      throw ValidationException('Reference number already exists');
    }
  }

  /// التحقق من صحة عناصر تسوية المخزون
  Future<void> _validateAdjustmentItems(List<StockAdjustmentItem> items) async {
    if (items.isEmpty) {
      throw ValidationException('At least one item is required');
    }

    for (final item in items) {
      if (item.productId.trim().isEmpty) {
        throw ValidationException('Product is required for all items');
      }

      if (item.adjustmentQuantity <= 0) {
        throw ValidationException(
            'Adjustment quantity must be greater than zero');
      }

      if (item.adjustmentType != 'increase' &&
          item.adjustmentType != 'decrease') {
        throw ValidationException('Invalid adjustment type');
      }

      if (item.reason.trim().isEmpty) {
        throw ValidationException('Reason is required for all items');
      }
    }
  }

  /// الحصول على معرف الوحدة الأساسية للمنتج
  Future<String> _getProductBaseUnitId(String productId) async {
    try {
      final product = await _productDao.findById(productId);
      if (product?.baseUnitId != null && product!.baseUnitId!.isNotEmpty) {
        return product.baseUnitId!;
      }
      // Return a default unit ID if product doesn't have a base unit
      return 'default_unit_id';
    } catch (e) {
      AppUtils.logError('Error getting product base unit ID', e);
      // Return a default unit ID in case of error
      return 'default_unit_id';
    }
  }

  /// معالجة عنصر تسوية المخزون
  Future<void> _processStockAdjustmentItem(
    StockAdjustmentItem item,
    String warehouseId,
    String userId,
  ) async {
    try {
      // Get product's base unit ID
      final unitId = await _getProductBaseUnitId(item.productId);

      // Create stock movement
      final movement = StockMovement.create(
        id: AppUtils.generateId(),
        productId: item.productId,
        warehouseId: warehouseId,
        movementType: item.adjustmentType,
        quantity: item.adjustmentQuantity,
        unitCost: 0.0, // Cost not relevant for adjustments
        unitId: unitId,
        referenceType: 'stock_adjustment',
        referenceId: item.adjustmentId,
        notes: '${item.reason} - ${item.notes}',
        createdBy: userId,
      );

      await _movementDao.insert(movement);

      // Update stock
      await _stockDao.createOrUpdateStock(
        productId: item.productId,
        warehouseId: warehouseId,
        quantity: item.newQuantity,
        averageCost: 0.0, // Don't change average cost for adjustments
      );

      AppUtils.logInfo('Processed stock adjustment item: ${item.id}');
    } catch (e) {
      AppUtils.logError('Error processing stock adjustment item', e);
      rethrow;
    }
  }
}
